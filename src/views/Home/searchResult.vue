<template>
  <div class="container mx-auto">
    <ElRow :gutter="10">
      <ElCol :span="5">
        <div class="mt-12">搜索进度：</div>
        <ElDivider />
        <div class="demo-progress">
          <ElSkeleton :loading="completedTypeLoading" animated>
            <ElProgress
              v-for="(item, key) in completedTypesList"
              :key="key"
              :percentage="100"
              class="my-2"
              :text-inside="true"
              :stroke-width="30"
              :status="item.ifCompleted ? 'success' : 'exception'"
            >
              {{ item.text }}
            </ElProgress>
          </ElSkeleton>
        </div>
      </ElCol>
      <ElDivider direction="vertical" style="height: auto" />
      <ElCol :span="13">
        <ElForm class="mt-4 flex flex-col w-full" style="min-height: 100%">
          <div class="flex flex-col items-center justify-center">
            <div style="width: 100%">
              <!-- 搜索类型 -->
              <div class="mb-4 flex justify-start items-center">
                <ElRadioGroup v-model="esType" class="flex items-center">
                  <ElRadio :value="1" size="large">精确搜索</ElRadio>
                  <!-- <ElRadio :value="2" size="large">前缀搜索</ElRadio>
                  <ElRadio :value="3" size="large">后缀搜索</ElRadio> -->
                  <ElRadio :value="5" size="large">模糊搜索</ElRadio>
                </ElRadioGroup>
              </div>
              <!-- 搜索框 -->
              <div class="mb-4 flex items-center gap-4" style="width: 100%">
                <div class="flex-1" tabindex="-1">
                  <ElInput
                    v-model="keyword"
                    placeholder="请输入关键词进行搜索"
                    class="input-with-select"
                    ref="searchInputRef"
                    @click="onInputClick"
                    @blur="onInputBlur"
                  >
                    <template #prepend>
                      <ElSelect
                        clearable
                        v-model="searchField"
                        placeholder="选择类型"
                        style="width: 150px; margin-right: 20px"
                        size="large"
                      >
                        <ElOption
                          v-for="(item, key) in fields"
                          :key="key"
                          :value="item.key"
                          :label="item.name"
                        />
                      </ElSelect>
                      <ElSelect
                        multiple
                        collapse-tags
                        clearable
                        v-model="searchCountries"
                        placeholder="选择国家"
                        style="width: 150px; margin-right: 20px"
                        size="large"
                      >
                        <template #header>
                          <ElInput
                            placeholder="请输入关键字进行搜索"
                            v-model="countryInput"
                            @input="countryFilter"
                        /></template>
                        <ElOption
                          v-for="(item, key) in countriesList"
                          :key="key"
                          :value="item.id"
                          :label="`${item.name}${item.code}`"
                        >
                          <div class="flex justify-between">
                            <span>{{ item.name }}</span>
                            <span>{{ item.code }}</span>
                          </div>
                        </ElOption>
                      </ElSelect>
                      <ElSelect
                        multiple
                        collapse-tags
                        clearable
                        v-model="searchLanguages"
                        placeholder="选择语言"
                        style="width: 150px"
                        size="large"
                      >
                        <template #header>
                          <ElInput
                            placeholder="请输入关键字进行搜索"
                            v-model="languageInput"
                            @input="languageFilter"
                        /></template>
                        <ElOption
                          v-for="(item, key) in languagesList"
                          :key="key"
                          :value="item.id"
                          :label="item.name"
                        />
                      </ElSelect>
                      <ElDatePicker
                        v-model="searchDate"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        value-format="YYYY-MM-DD"
                        class="date-picker-compact"
                        size="large"
                        clearable
                        style="margin-left: 20px"
                      />
                    </template>
                    <template #append>
                      <ElButton size="large" @click="toSearch" :loading="btnLoading"
                        >搜索一下</ElButton
                      >
                    </template>
                  </ElInput>
                </div>
              </div>
              <ElPopover
                v-model:visible="showRecordPopover"
                :virtual-ref="inputDomRef"
                virtual-triggering
                placement="bottom-start"
                width="600"
                popper-class="recent-record-popover"
              >
                <template #default>
                  <div v-if="recentlySearchRecord && recentlySearchRecord.length > 0">
                    <div class="recent-record-header">
                      <span class="recent-record-title">最近搜索</span>
                      <ElButton
                        size="small"
                        text
                        @click="deleteRecentlySearchRecord"
                        class="delete-all-btn"
                      >
                        删除所有记录
                      </ElButton>
                    </div>
                    <div class="recent-record-list">
                      <div
                        v-for="(item, idx) in recentlySearchRecord"
                        :key="idx"
                        @mousedown.prevent="selectRecord(item as any)"
                        class="recent-record-item"
                      >
                        <span>{{ (item as any).search_keyword }}</span>
                        <span v-if="(item as any).searchField" class="search-field-tag"
                          >({{ (item as any).searchField }})</span
                        >
                      </div>
                    </div>
                  </div>
                  <div v-else class="no-record-tip">暂无最近搜索</div>
                </template>
              </ElPopover>
            </div>

            <div v-if="isSearch" style="width: 100%">
              <ElSkeleton class="my-2" :loading="loading" :rows="5" animated>
                <div v-loading="pageLoading">
                  <div class="content" v-if="dataList && dataList.length > 0">
                    <!-- 排序选择器 - 只在有结果时显示 -->
                    <div class="sort-container flex justify-end items-center">
                      <span class="sort-label">排序方式：</span>
                      <ElSelect
                        v-model="order"
                        style="width: 200px"
                        size="default"
                        @change="toggleOrder"
                        placeholder="选择排序"
                      >
                        <ElOption value="desc" label="时间降序（最新优先）" />
                        <ElOption value="asc" label="时间升序（最早优先）" />
                      </ElSelect>
                    </div>
                    <ContentWrap class="mt-3" v-for="(item, index) in dataList" :key="index">
                      <Descriptions
                        :column="3"
                        :title="`${item.cid} - [详情]`"
                        :href="`/home/<USER>/result/${item.cid.trim()}?taskId=${taskId}`"
                        :data="item"
                        :schema="schema"
                        @href-click="handleHrefClick(item.cid)"
                      />
                    </ContentWrap>
                    <ContentWrap class="mt-5">
                      <div class="flex items-center mt-2">
                        <ElSelect v-model="pageSize" style="width: 128px" @change="sizeChange">
                          <ElOption :value="10" label="10条/页" />
                          <ElOption :value="50" label="50条/页" />
                          <ElOption :value="100" label="100条/页" />
                        </ElSelect>
                        <ElButton
                          type="primary"
                          class="ml-2"
                          :disabled="currentPage === 1"
                          :icon="ArrowLeft"
                          @click="prevClick"
                        />
                        <ElButton
                          type="primary"
                          class="ml-2"
                          :icon="ArrowRight"
                          :disabled="nextBtnDisabled"
                          @click="nextClick"
                        />
                      </div>
                    </ContentWrap>
                  </div>
                  <ContentWrap class="mt-5" v-else>
                    <ElEmpty description="没有可用的数据" />
                  </ContentWrap>
                </div>
              </ElSkeleton>
            </div>
          </div>
        </ElForm>
      </ElCol>
      <ElDivider direction="vertical" style="height: auto" />
      <ElCol :span="5">
        <div class="mt-12">
          <div>搜索模块 命中数量统计：</div>
          <ElDivider />
          <ElSkeleton :loading="typeResLoading" animated>
            <span v-if="typeResList.filter((i) => i.cid_total > 0).length > 0">
              <span v-for="(item, key) in typeResList" :key="key">
                <ElTag
                  v-if="item.cid_total > 0"
                  class="m-[2px] cursor-pointer"
                  @click="pushFilter(item)"
                  :type="searchModules.includes(item.search_type) ? 'success' : 'primary'"
                >
                  {{ item.search_type_name }}（{{ item.cid_total }}）
                </ElTag>
              </span>
            </span>
            <ElAlert
              v-else
              title="未查询到结果"
              type="warning"
              center
              show-icon
              :closable="false"
            />
          </ElSkeleton>
        </div>

        <div class="mt-12">
          <div>国家/地区 命中数量统计：</div>
          <ElDivider />
          <ElSkeleton :loading="countryListLoading" animated>
            <div v-if="countryList.length > 0">
              <ElTag
                class="m-[2px] cursor-pointer"
                :type="searchCountries.includes(+item.id) ? 'success' : 'primary'"
                v-for="(item, key) in countryList"
                :key="key"
                @click="pushCountry(item)"
                >{{ item.name }} - {{ item.code }}（{{ item.cid_total }}）</ElTag
              >
            </div>
            <ElAlert
              v-else
              title="未查询到结果"
              type="warning"
              center
              show-icon
              :closable="false"
            />
          </ElSkeleton>
        </div>

        <div class="mt-12">
          <div>语言 命中数量统计：</div>
          <ElDivider />
          <ElSkeleton :loading="languageListLoading" animated>
            <div v-if="languageList.length > 0">
              <ElTag
                class="m-[2px] cursor-pointer"
                :type="searchLanguages.includes(+item.id) ? 'success' : 'primary'"
                v-for="(item, key) in languageList"
                :key="key"
                @click="pushLanguage(item)"
                >{{ item.name }}（{{ item.cid_total }}）</ElTag
              >
            </div>
            <ElAlert
              v-else
              title="未查询到结果"
              type="warning"
              center
              show-icon
              :closable="false"
            />
          </ElSkeleton>
        </div>
      </ElCol>
    </ElRow>
  </div>
</template>

<script lang="tsx" setup>
import {
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElForm,
  ElMessage,
  ElMessageBox,
  ElRow,
  ElCol,
  ElDivider,
  ElTag,
  ElRadioGroup,
  ElRadio,
  ElSkeleton,
  // ElPagination,
  ElProgress,
  ElEmpty,
  ElAlert,
  ElPopover,
  ElDatePicker
} from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

import { onMounted, reactive, ref, nextTick } from 'vue'
import {
  createSearchRecordApi,
  getRecentlySearchRecordApi,
  deleteRecentlySearchRecordApi
} from '@/api/searchRecord'
import {
  getCidListApi,
  getSearchScheduleAPI,
  getSearchModulesAPI,
  getSearchCountryAPI,
  getSearchLanguageAPI,
  getNewResultListApi
} from '@/api/es'
import { checkCidAuthFromPageApi } from '@/api/search'
import { ContentWrap } from '@/components/ContentWrap'
import { Descriptions } from '@/components/Descriptions'
import { useRoute, useRouter } from 'vue-router'

import { useUserStore } from '@/store/modules/user'
import { getFieldsApi } from '@/api/home'
import { getPushConnection } from '@/utils/push'

import { filterCountry, filterLanguage } from '@/utils/filter'

import { useDictStore } from '@/store/modules/dict'

const dictStore = useDictStore()

const esType = ref(1)

const btnLoading = ref(false)

const keyword = ref('')
const searchCountries = ref([]) as any
const searchLanguages = ref([]) as any
const searchField = ref(null) as any
const searchModules = ref([]) as any
const searchDate = ref<[string, string] | undefined>(undefined)
const order = ref<'asc' | 'desc'>('desc')

// 用于跟踪上次搜索的关键词和时间筛选
const lastSearchKeyword = ref('')
const lastSearchDate = ref<[string, string] | undefined>(undefined)

const countriesList = ref([]) as any
const languagesList = ref([]) as any
const fields = ref([]) as any

const originCountriesList = ref([])
const originLanguagesList = ref([])

const typeResList = ref([]) as any
const countryList = ref([]) as any
const languageList = ref([]) as any
const completedTypesList = ref([]) as any

const currentPage = ref(1)
const pageSize = ref(10)
const lastId = ref(0)
const previousId = ref(0)

const isSearch = ref(false)
const loading = ref(false)

const pageLoading = ref(false)
const dataList = ref([]) as any
const nextBtnDisabled = ref(true)
const currentSearchParams = ref({})

const completedTypeLoading = ref(true)
const typeResLoading = ref(true)
const countryListLoading = ref(true)
const languageListLoading = ref(true)

const websocketMessageKey = ref(null)
const taskId = ref(null)

const recentlySearchRecord = ref([])
const showRecordPopover = ref(false)
const searchInputRef = ref(null)
const inputDomRef = ref(null)

const onInputClick = () => {
  nextTick(() => {
    const inputRef: any = searchInputRef.value
    inputDomRef.value =
      inputRef && inputRef.input ? inputRef.input : inputRef?.$el?.querySelector('input') || null
    showRecordPopover.value = true
  })
}

const onInputBlur = () => {
  setTimeout(() => {
    showRecordPopover.value = false
  }, 120)
}
const selectRecord = (item) => {
  keyword.value = item.search_keyword
  searchField.value = item.searchField || null
  showRecordPopover.value = false
}

// 移除关键词变化时的时间重置逻辑

const toggleOrder = () => {
  // 排序改变时更新 currentSearchParams 中的 order 值
  currentSearchParams.value = {
    ...currentSearchParams.value,
    order: order.value
  }

  // 重置分页参数
  currentPage.value = 1
  lastId.value = 0
  previousId.value = 0

  // 重新触发 searchCid
  searchCid()
}

const deleteRecentlySearchRecord = async () => {
  try {
    await ElMessageBox.confirm('确定要删除所有搜索记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deleteRecentlySearchRecordApi()
    recentlySearchRecord.value = []
    showRecordPopover.value = false
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

const searchCid = async () => {
  try {
    const { data } = await getCidListApi({
      ...currentSearchParams.value,
      taskId: taskId.value,
      last_id: lastId.value,
      previous_id: previousId.value
    })

    const { items, ifNext, ifCompleted } = data || {}

    if ((items && items.length > 0) || ifCompleted) {
      dataList.value = items as any
      nextBtnDisabled.value = !ifNext

      loading.value = false
    }

    return data // 确保返回结果
  } catch (error) {
    console.error('searchCid error:', error)
    throw error // 抛出错误以便被上层捕获
  }
}

const searchContent = async () => {
  isSearch.value = true
  loading.value = true
  btnLoading.value = true

  const searchParams = {
    keyword: keyword.value,
    search_field: searchField.value,
    search_modules: searchModules.value,
    es_type: esType.value,
    countries: searchCountries.value,
    languages: searchLanguages.value,
    searchDate: searchDate.value,
    from: currentPage.value,
    size: pageSize.value,
    order: order.value
  }
  currentSearchParams.value = searchParams

  try {
    const { data } = await getNewResultListApi(currentSearchParams.value)
    websocketMessageKey.value = data.messageKey
    taskId.value = data.taskId

    // 开始消息监听
    listenMessage()

    // 数据拉取 以及右边状态统计
    await Promise.all([searchCid(), getCidStat()])
  } finally {
    btnLoading.value = false
  }
}

const pushFilter = (item) => {
  if (!searchModules.value.includes(+item.search_type)) {
    searchModules.value.push(+item.search_type)
  } else {
    searchModules.value = searchModules.value.filter((module) => module !== +item.search_type)
  }
  currentPage.value = 1
  lastId.value = 0
  previousId.value = 0
  searchContent() // 重新搜索
}

const pushCountry = (item) => {
  if (!searchCountries.value.includes(+item.id)) {
    searchCountries.value.push(+item.id)
  } else {
    searchCountries.value = searchCountries.value.filter((country) => country !== +item.id)
  }
  currentPage.value = 1
  lastId.value = 0
  previousId.value = 0

  createSearchRecord()
  loadingPage()

  searchContent()
}

const loadingPage = () => {
  typeResLoading.value = true
  countryListLoading.value = true
  languageListLoading.value = true
  completedTypeLoading.value = true
}

const pushLanguage = (item) => {
  if (+item.id === -1) {
    return
  }
  if (!searchLanguages.value.includes(+item.id)) {
    searchLanguages.value.push(+item.id)
  } else {
    searchLanguages.value = searchLanguages.value.filter((language) => language !== +item.id)
  }
  currentPage.value = 1
  lastId.value = 0
  previousId.value = 0

  createSearchRecord()
  loadingPage()

  searchContent()
}

const getCidStat = async () => {
  try {
    // 使用 Promise.all 并行执行所有请求
    const [scheduleData, modulesData, countryData, languageData] = await Promise.all([
      // 获取搜索进度
      getSearchScheduleAPI({ taskId: taskId.value })
        .then((res) => {
          completedTypesList.value = res.data
          return res.data
        })
        .finally(() => {
          completedTypeLoading.value = false
        }),

      // 获取搜索模块数据
      getSearchModulesAPI({
        taskId: taskId.value,
        searchField: searchField.value,
        countries: searchCountries.value,
        languages: searchLanguages.value
      })
        .then((res) => {
          typeResList.value = res.data
          return res.data
        })
        .finally(() => {
          typeResLoading.value = false
        }),

      // 获取国家数据
      getSearchCountryAPI({
        taskId: taskId.value,
        searchField: searchField.value,
        countries: searchCountries.value,
        languages: searchLanguages.value,
        searchModules: searchModules.value
      })
        .then((res) => {
          countryList.value = res.data
          return res.data
        })
        .finally(() => {
          countryListLoading.value = false
        }),

      // 获取语言数据
      getSearchLanguageAPI({
        taskId: taskId.value,
        searchField: searchField.value,
        countries: searchCountries.value,
        languages: searchLanguages.value,
        searchModules: searchModules.value
      })
        .then((res) => {
          languageList.value = res.data
          return res.data
        })
        .finally(() => {
          languageListLoading.value = false
        })
    ])

    return {
      scheduleData,
      modulesData,
      countryData,
      languageData
    }
  } catch (error) {
    console.error('getCidStat error:', error)
    throw error // 抛出错误以便被上层捕获
  }
}

const createSearchRecord = () => {
  const apiParams = {
    keyword: keyword.value,
    searchField: searchField.value,
    esType: esType.value,
    searchCountries: searchCountries.value,
    searchLanguages: searchLanguages.value,
    searchDate: searchDate.value,
    order: order.value,
    type: 1
  }

  // 记录日志
  createSearchRecordApi(apiParams)
}

const toSearch = async () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }
  if (keyword.value.length < 2) {
    ElMessage.error('关键字最小长度为2个字符')
    return
  }
  if (!searchField.value) {
    ElMessage.error('请选择搜索类型')
    return
  }

  if (searchField.value === 'email' && keyword.value.indexOf('@') === -1) {
    ElMessage.error('请输入正确的邮箱地址')
    return
  }

  // 智能时间筛选重置逻辑
  const keywordChanged = lastSearchKeyword.value !== keyword.value
  const dateUnchanged = JSON.stringify(lastSearchDate.value) === JSON.stringify(searchDate.value)

  if (keywordChanged && searchDate.value && dateUnchanged) {
    // 关键词改变了，但时间筛选没有改变，则清空时间筛选
    searchDate.value = undefined

    // 从URL中移除时间参数
    const currentQuery = { ...router.currentRoute.value.query }
    delete currentQuery.searchDate
    router.replace({ query: currentQuery })
  }

  // 更新上次搜索的记录
  lastSearchKeyword.value = keyword.value
  lastSearchDate.value = searchDate.value ? ([...searchDate.value] as [string, string]) : undefined

  createSearchRecord()

  loadingPage()

  typeResList.value = []
  countryList.value = []
  languageList.value = []
  completedTypesList.value = []
  dataList.value = []

  // searchCountries.value = []
  // searchLanguages.value = []
  searchModules.value = []

  currentPage.value = 1
  lastId.value = 0
  previousId.value = 0
  searchContent()
}

const sizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  lastId.value = 0
  previousId.value = 0

  loadingPage()
  searchContent()
}

const handleHrefClick = async (cid) => {
  pageLoading.value = true
  try {
    await checkCidAuthFromPageApi({ cid, source: 2 })
  } finally {
    pageLoading.value = false
  }

  const href = '/home/<USER>/result/' + cid + '?taskId=' + taskId.value
  window.open(href, '_blank')
}

const getCountriesData = async () => {
  const data = await dictStore.fetchCountryList()

  countriesList.value = data as any
  originCountriesList.value = data as any
}

const getLanguagesData = async () => {
  const data = await dictStore.fetchLanguageList()

  languagesList.value = data
  originLanguagesList.value = data
}

const getFields = async () => {
  const { data } = await getFieldsApi()
  fields.value = data
}

const countryInput = ref(null)
const countryFilter = (val) => {
  if (!val) {
    countriesList.value = originCountriesList.value
  } else {
    countriesList.value = countriesList.value.filter((item: any) => {
      return item.name.indexOf(val) > -1 || item.code.toUpperCase().indexOf(val.toUpperCase()) > -1
    })
  }
}

const languageInput = ref(null)
const languageFilter = (val) => {
  if (!val) {
    languagesList.value = originLanguagesList.value
  } else {
    languagesList.value = languagesList.value.filter((item: any) => {
      return item.name.indexOf(val) > -1
    })
  }
}

const prevClick = () => {
  currentPage.value = currentPage.value - 1
  lastId.value = 0
  previousId.value = dataList.value[0]['id']
  searchContent()
}
const nextClick = () => {
  currentPage.value = currentPage.value + 1
  previousId.value = 0
  lastId.value = dataList.value[dataList.value.length - 1]['id']
  searchContent()
}

const listenMessage = () => {
  // 建立连接
  var connection = getPushConnection()
  const userStore = useUserStore()

  // 创建监听频道
  var user_channel = connection.subscribe('cid-search-' + userStore.getUserInfo.id)
  if (websocketMessageKey.value) {
    user_channel.on('cid-stat-' + websocketMessageKey.value, function (message) {
      // message是消息内容
      console.log('收到Stat message', message)
      getCidStat()
    })

    user_channel.on('cid-list-' + websocketMessageKey.value, function (message) {
      // message是消息内容
      console.log('收到List message', message)
      if (dataList.value.length < pageSize.value) {
        searchCid()
      }
    })
  }
}

const { query } = useRoute()
const router = useRouter()

const getRecentlySearchRecord = async () => {
  const { data } = await getRecentlySearchRecordApi()
  recentlySearchRecord.value = data
}

onMounted(() => {
  getCountriesData()
  getLanguagesData()
  getFields()
  getRecentlySearchRecord()

  let {
    keyword: kv,
    searchField: formatSearchField,
    esType: formatEsType,
    searchCountries: formatSearchCountries,
    searchLanguages: formatSearchLanguages,
    searchDate: formatDate,
    order: formatOrder
  } = query || {}

  keyword.value = kv as any
  esType.value = formatEsType ? +formatEsType : 1
  searchField.value = formatSearchField as any

  // 国家
  if (formatSearchCountries) {
    if (Array.isArray(formatSearchCountries) && formatSearchCountries.length > 0) {
      searchCountries.value = formatSearchCountries.map((item) => Number(item)) as any
    } else {
      searchCountries.value = [+formatSearchCountries] as any
    }
  }

  // 语言
  if (formatSearchLanguages) {
    if (Array.isArray(formatSearchLanguages) && formatSearchLanguages.length > 0) {
      searchLanguages.value = formatSearchLanguages.map((item) => Number(item)) as any
    } else {
      searchLanguages.value = [+formatSearchLanguages] as any
    }
  }

  // 时间
  if (formatDate) {
    if (Array.isArray(formatDate) && formatDate.length === 2) {
      searchDate.value = formatDate as any
    }
  }

  // 排序
  if (formatOrder && (formatOrder === 'asc' || formatOrder === 'desc')) {
    order.value = formatOrder as 'asc' | 'desc'
  }

  // 初始化上次搜索的记录
  lastSearchKeyword.value = keyword.value
  lastSearchDate.value = searchDate.value ? ([...searchDate.value] as [string, string]) : undefined

  searchContent()
})

const schema = reactive([
  {
    field: 'modules',
    label: '命中模块',
    width: '10%',
    span: 3,
    slots: {
      default: ({ modules }) => {
        return modules.map((item, index) => (
          <ElTag type="primary" class="mr-1" key={index}>
            {item}
          </ElTag>
        ))
      }
    }
  },
  {
    field: 'ip_address',
    label: 'IP',
    width: '10%'
  },
  {
    field: 'country',
    label: '国家/地区',
    width: '10%',
    slots: {
      default: ({ country }) => {
        const countryName = filterCountry(country)
        return <span>{countryName}</span>
      }
    }
  },
  {
    field: 'language',
    label: '语言',
    width: '10%',
    slots: {
      default: ({ language }) => {
        const languageName = filterLanguage(language)
        return <span>{languageName}</span>
      }
    }
  },
  {
    field: 'user',
    label: '用户名',
    width: '10%'
  },
  {
    field: 'os_version',
    label: '操作系统',
    width: '10%',
    span: 2
  },
  {
    field: 'format_log_date',
    label: '数据记录时间',
    width: '10%'
  }
  // {
  //   field: 'data_create_time',
  //   label: '数据入库时间',
  //   width: '10%'
  // }
])
</script>

<style scoped>
:deep(.el-progress-bar__inner) {
  text-align: center !important;
}

.date-picker-compact {
  width: 280px !important;
  min-width: 280px !important;
  max-width: 280px !important;
  flex-shrink: 0;
}

:deep(.date-picker-compact) {
  width: 280px !important;
  min-width: 280px !important;
  max-width: 280px !important;
}

/* 时间选择器样式修复 - 完全匹配其他 large 组件 */
/* :deep(.date-picker-compact) {
  width: 200px !important;
  min-width: 200px !important;
  max-width: 200px !important;
} */

:deep(.el-input-group__prepend) {
  padding-right: 0 !important;
}

.recent-record-popover {
  background: #222 !important;
  color: #fff !important;
  border: 1px solid #444 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.recent-record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #333;
  border-bottom: 1px solid #444;
  margin: 0;
}

.recent-record-title {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.delete-all-btn {
  color: #ff6b6b !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: auto !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.delete-all-btn:hover {
  background: rgba(255, 107, 107, 0.1) !important;
  color: #ff8a8a !important;
}

.recent-record-list {
  max-height: 300px;
  overflow-y: auto;
}

.recent-record-item {
  padding: 12px 16px;
  cursor: pointer;
  color: #fff;
  border-bottom: 1px solid #333;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.recent-record-item:last-child {
  border-bottom: none;
}

.recent-record-item:hover {
  background: #333;
  padding-left: 20px;
}

.search-field-tag {
  color: #aaa;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 8px;
}

.no-record-tip {
  padding: 20px 16px;
  color: #aaa;
  text-align: center;
  font-size: 14px;
}

/* 滚动条样式 */
.recent-record-list::-webkit-scrollbar {
  width: 4px;
}

.recent-record-list::-webkit-scrollbar-track {
  background: #333;
}

.recent-record-list::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 2px;
}

.recent-record-list::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* 排序按钮样式 */
.sort-icon-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-icon-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sort-icon-btn:active {
  transform: scale(0.95);
}

/* 重写container样式 */
.container {
  max-width: 100%;
  margin: 0 auto;
  padding-left: 8px;
  padding-right: 8px;
}

@media (min-width: 640px) {
  .container {
    padding-left: 12px;
    padding-right: 12px;
  }
}

@media (min-width: 768px) {
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (min-width: 1280px) {
  .container {
    padding-left: 24px;
    padding-right: 24px;
  }
}
</style>
